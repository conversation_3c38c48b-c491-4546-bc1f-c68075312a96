{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\dependencyresolvers\\autofac\\autofacbusinessmodule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\dependencyresolvers\\autofac\\autofacbusinessmodule.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efmemberdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efmemberdal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\unifiedcompanymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\unifiedcompanymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\usercompanymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\usercompanymanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\dataaccess\\concrete\\entityframework\\efexpensedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{4AA34150-7DB7-4EDD-B68A-21C077BF3383}|DataAccess\\DataAccess.csproj|solutionrelative:dataaccess\\concrete\\entityframework\\efexpensedal.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\package-lock.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\package-lock.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\appsettings.development.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\properties\\launchsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\aac3bdc7f5662e5c2b8de73562aa0880c37ecaf568f8493b863340a5264741ce\\Core\\Utilities\\Security\\JWT\\JwtHelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\paymentmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\paymentmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\webapi\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{13C9E323-C28C-45AB-AB23-01288EE0D508}|WebAPI\\WebAPI.csproj|solutionrelative:webapi\\controllers\\usercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\transactionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\transactionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|c:\\users\\<USER>\\desktop\\gymproject\\gymprojectbackend\\business\\concrete\\companyusermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B9F61420-E4BF-43BB-BA09-AA3397A11C25}|Business\\Business.csproj|solutionrelative:business\\concrete\\companyusermanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 149, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "AutofacBusinessModule.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\DependencyResolvers\\Autofac\\AutofacBusinessModule.cs", "RelativeDocumentMoniker": "Business\\DependencyResolvers\\Autofac\\AutofacBusinessModule.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\DependencyResolvers\\Autofac\\AutofacBusinessModule.cs", "RelativeToolTip": "Business\\DependencyResolvers\\Autofac\\AutofacBusinessModule.cs", "ViewState": "AgIAAFcAAAAAAAAAAAAuwG0AAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T21:19:35.816Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "EfExpenseDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfExpenseDal.cs", "ViewState": "AgIAAHAAAAAAAAAAAAAAAIkAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T12:07:00.568Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "JwtHelper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\aac3bdc7f5662e5c2b8de73562aa0880c37ecaf568f8493b863340a5264741ce\\Core\\Utilities\\Security\\JWT\\JwtHelper.cs", "RelativeDocumentMoniker": "..\\..\\..\\AppData\\Local\\SourceServer\\aac3bdc7f5662e5c2b8de73562aa0880c37ecaf568f8493b863340a5264741ce\\Core\\Utilities\\Security\\JWT\\JwtHelper.cs", "ToolTip": "C:\\Users\\<USER>\\AppData\\Local\\SourceServer\\aac3bdc7f5662e5c2b8de73562aa0880c37ecaf568f8493b863340a5264741ce\\Core\\Utilities\\Security\\JWT\\JwtHelper.cs", "RelativeToolTip": "..\\..\\..\\AppData\\Local\\SourceServer\\aac3bdc7f5662e5c2b8de73562aa0880c37ecaf568f8493b863340a5264741ce\\Core\\Utilities\\Security\\JWT\\JwtHelper.cs", "ViewState": "AgIAACUAAAAAAAAAAAAiwEAAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-01T00:28:03.804Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "package-lock.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\package-lock.json", "RelativeDocumentMoniker": "WebAPI\\package-lock.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\package-lock.json", "RelativeToolTip": "WebAPI\\package-lock.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-01T00:10:11.864Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "PaymentManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\PaymentManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\PaymentManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\PaymentManager.cs", "RelativeToolTip": "Business\\Concrete\\PaymentManager.cs", "ViewState": "AgIAADkAAAAAAAAAAAASwGIAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T14:31:22.938Z"}, {"$type": "Document", "DocumentIndex": 1, "Title": "EfMemberDal.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "RelativeDocumentMoniker": "DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "RelativeToolTip": "DataAccess\\Concrete\\EntityFramework\\EfMemberDal.cs", "ViewState": "AgIAAB8DAAAAAAAAAAAUwAADAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T13:31:07.313Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "UserCompanyManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserCompanyManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\UserCompanyManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UserCompanyManager.cs", "RelativeToolTip": "Business\\Concrete\\UserCompanyManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T18:25:32.86Z"}, {"$type": "Document", "DocumentIndex": 2, "Title": "UnifiedCompanyManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UnifiedCompanyManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\UnifiedCompanyManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\UnifiedCompanyManager.cs", "RelativeToolTip": "Business\\Concrete\\UnifiedCompanyManager.cs", "ViewState": "AgIAABAAAAAAAAAAAADgvy4AAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-03T18:25:34.106Z"}, {"$type": "Document", "DocumentIndex": 5, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Program.cs", "RelativeDocumentMoniker": "WebAPI\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Program.cs", "RelativeToolTip": "WebAPI\\Program.cs", "ViewState": "AgIAAC8AAAAAAAAAAAAAADcAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-29T13:09:10.82Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "launchSettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Properties\\launchSettings.json", "RelativeDocumentMoniker": "WebAPI\\Properties\\launchSettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Properties\\launchSettings.json", "RelativeToolTip": "WebAPI\\Properties\\launchSettings.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-29T13:08:59.103Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "UserController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\UserController.cs", "RelativeDocumentMoniker": "WebAPI\\Controllers\\UserController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\Controllers\\UserController.cs", "RelativeToolTip": "WebAPI\\Controllers\\UserController.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAC8AAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-28T15:17:58.511Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "appsettings.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.json", "RelativeDocumentMoniker": "WebAPI\\appsettings.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.json", "RelativeToolTip": "WebAPI\\appsettings.json", "ViewState": "AgIAADAAAAAAAAAAAAAAAB0AAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-28T15:03:50.803Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "appsettings.Development.json", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.Development.json", "RelativeDocumentMoniker": "WebAPI\\appsettings.Development.json", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\WebAPI\\appsettings.Development.json", "RelativeToolTip": "WebAPI\\appsettings.Development.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-06-28T15:02:05.603Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "TransactionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\TransactionManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\TransactionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\TransactionManager.cs", "RelativeToolTip": "Business\\Concrete\\TransactionManager.cs", "ViewState": "AgIAAMIAAAAAAAAAAAAjwPMAAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-27T12:32:39.384Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "CompanyUserManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyUserManager.cs", "RelativeDocumentMoniker": "Business\\Concrete\\CompanyUserManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\GymProject\\GymProjectBackend\\Business\\Concrete\\CompanyUserManager.cs", "RelativeToolTip": "Business\\Concrete\\CompanyUserManager.cs", "ViewState": "AgIAAF8AAAAAAAAAAAAjwIEAAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-26T20:06:18.095Z"}]}]}]}