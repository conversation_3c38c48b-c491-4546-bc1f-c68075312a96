﻿using Core.DataAccess.EntityFramework;
using Core.Entities.Concrete;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserOperationClaimDal : EfEntityRepositoryBase<UserOperationClaim, GymContext>, IUserOperationClaimDal
    {
        // Constructor injection (Scalability için)
        public EfUserOperationClaimDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfUserOperationClaimDal() : base()
        {
        }

        public List<UserOperationClaimDto> GetUserOperationClaimDetails()
        {
            var result = from uoc in _context.UserOperationClaims
                         join u in _context.Users on uoc.UserId equals u.UserID
                         join oc in _context.OperationClaims on uoc.OperationClaimId equals oc.OperationClaimId
                         select new UserOperationClaimDto
                         {
                             UserOperationClaimId = uoc.UserOperationClaimId,
                             UserId = uoc.UserId,
                             UserName = u.FirstName + " " + u.LastName,
                             OperationClaimId = uoc.OperationClaimId,
                             OperationClaimName = oc.Name,
                             Email=u.Email
                         };
            return result.ToList();
        }
    }
}
