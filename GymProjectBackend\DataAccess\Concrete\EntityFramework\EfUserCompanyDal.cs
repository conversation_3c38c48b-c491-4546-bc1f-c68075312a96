﻿using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfUserCompanyDal: EfEntityRepositoryBase<UserCompany, GymContext>, IUserCompanyDal
    {
        // Constructor injection (Scalability için)
        public EfUserCompanyDal(GymContext context) : base(context)
        {
        }

        // Backward compatibility constructor
        public EfUserCompanyDal() : base()
        {
        }

        public List<UserCompanyDetailDto> GetUserCompanyDetails()
        {
            var result = from uc in _context.UserCompanies
                         join cu in _context.CompanyUsers on uc.UserID equals cu.CompanyUserID
                         join c in _context.Companies on uc.CompanyId equals c.CompanyID
                         where uc.IsActive == true
                         select new UserCompanyDetailDto
                         {
                             UserCompanyId = uc.UserCompanyID,
                             CompanyUserName = cu.Name,
                             CompanyName = c.CompanyName,
                             isActive=uc.IsActive,
                         };
            return result.ToList();
        }

        public int GetUserCompanyId(int userId)
        {
            using (var context = new GymContext())
            {
                // Mevcut veritabanı tasarımına göre: UserCompany.UserID = CompanyUser.CompanyUserID
                // Bu yüzden önce User'ın email'i ile CompanyUser'ı bulup, sonra UserCompany'yi arıyoruz
                var user = context.Users.FirstOrDefault(u => u.UserID == userId);
                if (user == null) return -1;

                var companyUser = context.CompanyUsers.FirstOrDefault(cu => cu.Email == user.Email);
                if (companyUser == null) return -1;

                var userCompany = context.UserCompanies
                    .FirstOrDefault(uc => uc.UserID == companyUser.CompanyUserID && uc.IsActive == true);

                return userCompany?.CompanyId ?? -1;
            }
        }
    }
}
